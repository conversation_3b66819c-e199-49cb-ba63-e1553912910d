/**
 * 极验验证码使用示例
 * 展示如何在登录页面中使用 gt.js
 */

import geetestLoader from '../utils/geetest-loader.js';

/**
 * 登录页面的极验验证码集成示例
 */
class LoginGeetestExample {
  constructor() {
    this.captchaObj = null;
    this.isGeetestReady = false;
  }

  /**
   * 初始化极验验证码
   * @param {string} containerId 验证码容器的 DOM ID
   * @param {Object} geetestConfig 极验配置参数
   */
  async initGeetest(containerId, geetestConfig) {
    try {
      console.log('开始初始化极验验证码...');
      
      // 加载 gt.js 脚本
      const loaded = await geetestLoader.loadScript();
      if (!loaded) {
        throw new Error('无法加载极验验证码脚本');
      }

      // 默认配置
      const defaultConfig = {
        // 以下参数需要从后端获取
        gt: geetestConfig.gt || '',
        challenge: geetestConfig.challenge || '',
        offline: geetestConfig.offline || false,
        new_captcha: geetestConfig.new_captcha || true,
        
        // 产品配置
        product: 'bind', // 产品形式，包括：float，popup，bind
        width: '300px',
        lang: 'zh-cn', // 语言，可选值：zh-cn, en, ja, ko, th
        
        // 回调函数
        onReady: () => {
          console.log('极验验证码准备就绪');
          this.isGeetestReady = true;
          this.onGeetestReady();
        },
        onSuccess: () => {
          console.log('极验验证码验证成功');
          this.onGeetestSuccess();
        },
        onError: (error) => {
          console.error('极验验证码出错:', error);
          this.onGeetestError(error);
        },
        onClose: () => {
          console.log('极验验证码关闭');
          this.onGeetestClose();
        }
      };

      // 合并配置
      const finalConfig = { ...defaultConfig, ...geetestConfig };

      // 初始化极验
      this.captchaObj = await geetestLoader.initGeetest(finalConfig);
      
      // 将验证码绑定到指定容器
      this.captchaObj.appendTo(`#${containerId}`);
      
      console.log('极验验证码初始化完成');
      return this.captchaObj;
      
    } catch (error) {
      console.error('初始化极验验证码失败:', error);
      throw error;
    }
  }

  /**
   * 获取验证结果
   * @returns {Object|null} 验证结果对象
   */
  getValidateResult() {
    if (!this.captchaObj) {
      console.warn('极验验证码未初始化');
      return null;
    }

    const result = this.captchaObj.getValidate();
    if (!result) {
      console.warn('请先完成验证码验证');
      return null;
    }

    return {
      geetest_challenge: result.geetest_challenge,
      geetest_validate: result.geetest_validate,
      geetest_seccode: result.geetest_seccode
    };
  }

  /**
   * 重置验证码
   */
  reset() {
    if (this.captchaObj) {
      this.captchaObj.reset();
    }
  }

  /**
   * 销毁验证码实例
   */
  destroy() {
    if (this.captchaObj) {
      this.captchaObj.destroy();
      this.captchaObj = null;
    }
    this.isGeetestReady = false;
  }

  // 回调函数，可以被子类重写
  onGeetestReady() {
    // 验证码准备就绪时的处理
  }

  onGeetestSuccess() {
    // 验证成功时的处理
  }

  onGeetestError(error) {
    // 验证出错时的处理
  }

  onGeetestClose() {
    // 验证码关闭时的处理
  }
}

/**
 * 使用示例
 */
export function createGeetestExample() {
  return new LoginGeetestExample();
}

/**
 * 简化的使用方法
 * @param {string} containerId 容器ID
 * @param {Object} config 配置对象
 * @returns {Promise<Object>} 极验实例
 */
export async function initSimpleGeetest(containerId, config) {
  try {
    // 直接使用 geetestLoader
    await geetestLoader.loadScript();
    
    const captchaObj = await geetestLoader.initGeetest({
      ...config,
      product: 'bind',
      width: '100%'
    });
    
    captchaObj.appendTo(`#${containerId}`);
    return captchaObj;
    
  } catch (error) {
    console.error('简化初始化失败:', error);
    throw error;
  }
}

// HTML 使用示例（注释形式）
/*
<!DOCTYPE html>
<html>
<head>
    <title>极验验证码示例</title>
</head>
<body>
    <div id="captcha-container"></div>
    <button id="submit-btn">提交</button>
    
    <script type="module">
        import { initSimpleGeetest } from './geetest-usage-example.js';
        
        // 从后端获取极验配置
        async function getGeetestConfig() {
            // 这里应该调用后端 API 获取配置
            const response = await fetch('/api/geetest/register');
            return await response.json();
        }
        
        // 初始化验证码
        async function initCaptcha() {
            try {
                const config = await getGeetestConfig();
                const captchaObj = await initSimpleGeetest('captcha-container', config);
                
                // 监听提交按钮
                document.getElementById('submit-btn').addEventListener('click', () => {
                    const result = captchaObj.getValidate();
                    if (result) {
                        console.log('验证结果:', result);
                        // 提交到后端验证
                        submitLogin(result);
                    } else {
                        alert('请先完成验证码验证');
                    }
                });
                
            } catch (error) {
                console.error('初始化失败:', error);
            }
        }
        
        // 提交登录
        async function submitLogin(geetestResult) {
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'your-username',
                        password: 'your-password',
                        ...geetestResult
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    alert('登录成功');
                } else {
                    alert('登录失败: ' + result.message);
                }
            } catch (error) {
                console.error('登录失败:', error);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initCaptcha);
    </script>
</body>
</html>
*/

export default LoginGeetestExample;
