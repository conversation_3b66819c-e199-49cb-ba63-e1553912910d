(function (global, factory) {
  console.log('gt start loading');

  if (typeof module === 'object' && typeof module.exports === 'object') {
    // CommonJS
    module.exports = global.document
      ? factory(global, true)
      : function (w) {
          if (!w.document) {
            throw new Error('Geetest requires a window with a document');
          }
          return factory(w);
        };
  } else {
    factory(global);
  }
})(typeof window !== 'undefined' ? window : this, function (window, noGlobal) {
  if (typeof window === 'undefined') {
    throw new Error('Geetest requires browser environment');
  }
  const { document } = window;
  const { Math } = window;
  const head = document.getElementsByTagName('head')[0];

  function _Object(obj) {
    this._obj = obj;
  }

  _Object.prototype = {
    _each(process) {
      const { _obj } = this;
      for (const k in _obj) {
        if (_obj.hasOwnProperty(k)) {
          process(k, _obj[k]);
        }
      }
      return this;
    },
  };
  function Config(config) {
    const self = this;
    new _Object(config)._each(function (key, value) {
      self[key] = value;
    });
  }

  Config.prototype = {
    protocol: 'http://',
    _extend(obj) {
      const self = this;
      new _Object(obj)._each(function (key, value) {
        self[key] = value;
      });
    },
  };
  const isNumber = function (value) {
    return typeof value === 'number';
  };
  const isString = function (value) {
    return typeof value === 'string';
  };
  const isBoolean = function (value) {
    return typeof value === 'boolean';
  };
  const isObject = function (value) {
    return typeof value === 'object' && value !== null;
  };
  const isFunction = function (value) {
    return typeof value === 'function';
  };
  const callbacks = {};
  const status = {};
  const loadScript = function (url, cb) {
    const script = document.createElement('script');
    script.charset = 'UTF-8';
    script.async = true;
    script.onerror = function () {
      cb(true);
    };
    let loaded = false;
    script.onload = script.onreadystatechange = function () {
      if (
        !loaded &&
        (!script.readyState ||
          'loaded' === script.readyState ||
          'complete' === script.readyState)
      ) {
        loaded = true;
        setTimeout(function () {
          cb(false);
        }, 0);
      }
    };
    script.src = url;
    head.appendChild(script);
  };
  const normalizeDomain = function (domain) {
    return domain.replace(/^https?:\/\/|\/$/g, '');
  };
  const normalizePath = function (path) {
    path = path.replace(/\/+/g, '/');
    if (path.indexOf('/') !== 0) {
      path = `/${path}`;
    }
    return path;
  };
  const normalizeQuery = function (query) {
    if (!query) {
      return '';
    }
    let q = '?';
    new _Object(query)._each(function (key, value) {
      if (isString(value) || isNumber(value) || isBoolean(value)) {
        q = `${q + encodeURIComponent(key)}=${encodeURIComponent(value)}&`;
      }
    });
    if (q === '?') {
      q = '';
    }
    return q.replace(/&$/, '');
  };
  const makeURL = function (protocol, domain, path, query) {
    domain = normalizeDomain(domain);

    let url = normalizePath(path) + normalizeQuery(query);
    if (domain) {
      url = protocol + domain + url;
    }

    return url;
  };
  const load = function (protocol, domains, path, query, cb) {
    var tryRequest = function (at) {
      const url = makeURL(protocol, domains[at], path, query);
      loadScript(url, function (err) {
        if (err) {
          if (at >= domains.length - 1) {
            cb(true);
          } else {
            tryRequest(at + 1);
          }
        } else {
          cb(false);
        }
      });
    };
    tryRequest(0);
  };
  const throwError = function (errorType, config) {
    const errors = {
      networkError: '网络错误',
    };
    if (typeof config.onError === 'function') {
      config.onError(errors[errorType]);
    } else {
      throw new Error(errors[errorType]);
    }
  };
  const detect = function () {
    return !!window.Geetest;
  };
  if (detect()) {
    status.slide = 'loaded';
  }
  const initGeetest = function (userConfig, callback) {
    console.log('gt init loading');

    const config = new Config(userConfig);
    if (userConfig.https) {
      config.protocol = 'https://';
    } else if (!userConfig.protocol) {
      config.protocol = `${window.location.protocol}//`;
    }
    const { type } = config;
    const init = function () {
      callback(new window.Geetest(config));
    };
    callbacks[type] = callbacks[type] || [];
    const s = status[type] || 'init';
    if (s === 'init') {
      status[type] = 'loading';
      callbacks[type].push(init);
      load(
        config.protocol,
        config.static_servers,
        config[type],
        null,
        function (err) {
          if (err) {
            status[type] = 'fail';
            throwError('networkError', config);
          } else {
            status[type] = 'loaded';
            const cbs = callbacks[type];
            for (let i = 0, len = cbs.length; i < len; i += 1) {
              const cb = cbs[i];
              if (isFunction(cb)) {
                cb();
              }
            }
            callbacks[type] = [];
          }
        }
      );
    } else if (s === 'loaded') {
      init();
    } else if (s === 'fail') {
      throwError('networkError', config);
    } else if (s === 'loading') {
      callbacks[type].push(init);
    }
  };
  window.initGeetest = initGeetest;
  return initGeetest;
});
