/**
 * 设计登录表单演示入口
 * 用于预览和测试根据设计稿实现的登录界面
 */

import React from 'react';
import ReactDOM from 'react-dom';
import { DesignLoginDemo } from './components/DesignLoginDemo';

// 渲染演示组件
const renderDemo = () => {
  // 清空现有内容
  document.body.innerHTML = '<div id="design-login-root"></div>';

  // 获取容器并渲染React组件
  const container = document.getElementById('design-login-root');
  if (container) {
    ReactDOM.render(<DesignLoginDemo />, container);
  }
};

// 页面加载完成后渲染
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', renderDemo);
} else {
  renderDemo();
}

// 暴露到全局以便调试
(window as any).renderDesignLoginDemo = renderDemo;
