/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/button-has-type */
/* eslint-disable no-lonely-if */
/**
 * 登录应用主组件
 * 管理登录流程和不同登录组件的切换
 */

import React, { useState, useCallback } from 'react';
import { LoginForm } from './LoginForm';
import { LoadingSpinner } from './LoadingSpinner';
import { CaptchaVerify } from './CaptchaVerify';
import { PhoneVerify } from './PhoneVerify';
import { LoginManager } from '../services/LoginManager';
import GeeTest from '../utils/geetest';

interface LoginAppProps {
  loginManager: LoginManager;
  onLoginSuccess: () => void;
}

export type LoginStep = 'form' | 'captcha' | 'phone' | 'loading';

export const LoginApp: React.FC<LoginAppProps> = ({
  loginManager,
  onLoginSuccess,
}) => {
  const [currentStep, setCurrentStep] = useState<LoginStep>('form');
  const [loginData, setLoginData] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const handleFormSubmit = useCallback(
    async (formData: any) => {
      setError('');
      setCurrentStep('loading');
      setLoginData(formData);
      new GeeTest().initGt().then((res) => {
        //

        if (res?.retCode === 0) {
          // setChallengeHandler(res.value);
          // sendSMSVerifyCode(res.value);
        } else if (res.retCode === 99) {
          // 用户未通过人机行为验证
          // gt插件验证失败，请重试
        } else if (res.retCode === 100) {
          // 用户手动关闭行为验证
        } else {
          // 这里是调用后台验证流水号失败的接口返回
        }
      });
    },
    [loginManager, onLoginSuccess]
  );

  const handleCaptchaSuccess = useCallback(
    async (captchaData: any) => {
      // 处理极验验证成功
      setCurrentStep('loading');

      try {
        const result = await loginManager.performLoginWithCaptcha(
          loginData,
          captchaData
        );

        if (result.success) {
          loginManager.saveUserSession(result.token, loginData.username);
          onLoginSuccess();
        } else if (result.needPhoneVerify) {
          setCurrentStep('phone');
        } else {
          setError(result.message || '验证失败');
          setCurrentStep('form');
        }
      } catch (captchaError) {
        setError('验证失败，请重试');
        setCurrentStep('form');
      }
    },
    [loginManager, loginData, onLoginSuccess]
  );

  const handlePhoneVerifySuccess = useCallback(
    async (phoneData: any) => {
      // 处理手机验证成功
      setCurrentStep('loading');

      try {
        const result = await loginManager.performLoginWithPhone(
          loginData,
          phoneData
        );

        if (result.success) {
          loginManager.saveUserSession(result.token, loginData.username);
          onLoginSuccess();
        } else {
          setError(result.message || '手机验证失败');
          setCurrentStep('form');
        }
      } catch (phoneError) {
        setError('手机验证失败，请重试');
        setCurrentStep('form');
      }
    },
    [loginManager, loginData, onLoginSuccess]
  );

  const handleBackToForm = useCallback(() => {
    setCurrentStep('form');
    setError('');
  }, []);

  const handleCloseWindow = useCallback(() => {
    // 关闭窗口
    if ((window as any).electronAPI?.ipcSend) {
      (window as any).electronAPI.ipcSend('close-window');
    } else {
      window.close();
    }
  }, []);

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'form':
        return (
          <LoginForm
            onSubmit={handleFormSubmit}
            error={error}
            isLoading={false}
          />
        );

      case 'captcha':
        return (
          <CaptchaVerify
            onSuccess={handleCaptchaSuccess}
            onError={(errorMsg) => {
              setError(errorMsg);
              setCurrentStep('form');
            }}
            onCancel={handleBackToForm}
          />
        );

      case 'phone':
        return (
          <PhoneVerify
            onSuccess={handlePhoneVerifySuccess}
            onError={(errorMsg) => {
              setError(errorMsg);
              setCurrentStep('form');
            }}
            onCancel={handleBackToForm}
          />
        );

      case 'loading':
        return <LoadingSpinner message="正在登录..." />;

      default:
        return null;
    }
  };

  return (
    <div className="design-login-app">
      <div className="design-login-container">
        {/* 右上角关闭按钮 */}
        <button
          className="close-button"
          type="button"
          onClick={handleCloseWindow}
          title="关闭"
        >
          ✕
        </button>

        <div className="login-content">{renderCurrentStep()}</div>
      </div>
    </div>
  );
};
