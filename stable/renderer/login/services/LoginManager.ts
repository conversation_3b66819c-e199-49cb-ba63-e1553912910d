/**
 * 登录管理服务
 * 处理所有登录相关的业务逻辑
 */

export interface LoginResult {
  success: boolean;
  token?: string;
  message?: string;
  needCaptcha?: boolean;
  needPhoneVerify?: boolean;
}

export interface LoginConfig {
  apiEndpoint?: string;
  timeout?: number;
  retryAttempts?: number;
}

export class LoginManager {
  private readonly config: LoginConfig;

  constructor(config: LoginConfig = {}) {
    this.config = {
      apiEndpoint: '/api/login',
      timeout: 10000,
      retryAttempts: 3,
      ...config,
    };
  }

  /**
   * 带极验验证的登录
   */
  async performLoginWithCaptcha(
    loginData: any,
    captchaData: any
  ): Promise<LoginResult> {
    try {
      // 这里实现带极验验证的登录API调用
      const response = await this.mockLoginWithCaptchaAPI(
        loginData,
        captchaData
      );

      return response;
    } catch (error) {
      console.error('Login with captcha error:', error);
      return {
        success: false,
        message: '验证失败，请重试',
      };
    }
  }

  /**
   * 带手机验证的登录
   */
  async performLoginWithPhone(
    loginData: any,
    phoneData: any
  ): Promise<LoginResult> {
    try {
      // 这里实现带手机验证的登录API调用
      const response = await this.mockLoginWithPhoneAPI(loginData, phoneData);

      return response;
    } catch (error) {
      console.error('Login with phone error:', error);
      return {
        success: false,
        message: '手机验证失败，请重试',
      };
    }
  }

  /**
   * 保存用户会话信息
   */
  saveUserSession(token: string, username: string) {
    localStorage.setItem('userToken', token);
    localStorage.setItem('username', username);
    localStorage.setItem('loginTime', new Date().toISOString());
  }

  /**
   * 检查登录状态
   */
  checkLoginStatus(): boolean {
    const token = localStorage.getItem('userToken');
    const loginTime = localStorage.getItem('loginTime');

    if (!token || !loginTime) {
      return false;
    }

    // 检查token是否过期（示例：24小时过期）
    const loginDate = new Date(loginTime);
    const now = new Date();
    const hoursDiff = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60);

    if (hoursDiff > 24) {
      this.logout();
      return false;
    }

    return true;
  }

  /**
   * 登出
   */
  logout() {
    localStorage.removeItem('userToken');
    localStorage.removeItem('username');
    localStorage.removeItem('loginTime');
    // 保留rememberMe和savedUsername，用于下次登录
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return {
      username: localStorage.getItem('username'),
      token: localStorage.getItem('userToken'),
      loginTime: localStorage.getItem('loginTime'),
    };
  }

  // 以下是模拟API方法，实际使用时请替换为真实的API调用

  private async mockLoginAPI(
    username: string,
    password: string
  ): Promise<LoginResult> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟不同的登录场景
        if (username === 'admin' && password === 'password') {
          resolve({
            success: true,
            token: `mock-jwt-token-${Date.now()}`,
          });
        } else if (username === 'captcha' && password === 'test') {
          resolve({
            success: false,
            needCaptcha: true,
            message: '需要完成安全验证',
          });
        } else if (username === 'phone' && password === 'test') {
          resolve({
            success: false,
            needPhoneVerify: true,
            message: '需要手机验证',
          });
        } else {
          resolve({
            success: false,
            message: '用户名或密码错误',
          });
        }
      }, 1000);
    });
  }

  private async mockLoginWithCaptchaAPI(
    loginData: any,
    captchaData: any
  ): Promise<LoginResult> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟极验验证成功
        resolve({
          success: true,
          token: `mock-jwt-token-captcha-${Date.now()}`,
        });
      }, 1000);
    });
  }

  private async mockLoginWithPhoneAPI(
    loginData: any,
    phoneData: any
  ): Promise<LoginResult> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟手机验证成功
        resolve({
          success: true,
          token: `mock-jwt-token-phone-${Date.now()}`,
        });
      }, 1000);
    });
  }
}
